import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/jwt-middleware';
import { db } from '@/lib/db';
import { generatedEmails } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function handleGenerateEmail(request: AuthenticatedRequest) {
  try {
    // 读取名字列表
    const namesFilePath = path.join(process.cwd(), 'namelist', 'names.txt');
    const namesContent = fs.readFileSync(namesFilePath, 'utf-8');
    const names = namesContent.split('\n').filter(name => name.trim());

    let email: string;
    let attempts = 0;
    const maxAttempts = 100;

    do {
      // 随机选择一个名字
      const randomName = names[Math.floor(Math.random() * names.length)];

      // 处理名字：移除空格，转换为小写
      const cleanName = randomName.replace(/\s+/g, '').toLowerCase();

      // 生成4位随机数字的函数
      const generate4Digits = () => Math.floor(1000 + Math.random() * 9000).toString();

      // 随机决定数字组合模式：更多可能性
      const patterns = [
        'front-only',      // 只在前面
        'middle-only',     // 只在中间
        'back-only',       // 只在后面
        'front-middle',    // 前面和中间
        'middle-back',     // 中间和后面
        'front-back',      // 前面和后面
        'all-three'        // 前面、中间和后面
      ];

      const selectedPattern = patterns[Math.floor(Math.random() * patterns.length)];
      let emailPrefix: string;

      const midPoint = Math.floor(cleanName.length / 2);
      const frontPart = cleanName.substring(0, midPoint);
      const backPart = cleanName.substring(midPoint);

      switch (selectedPattern) {
        case 'front-only':
          emailPrefix = generate4Digits() + cleanName;
          break;
        case 'middle-only':
          emailPrefix = frontPart + generate4Digits() + backPart;
          break;
        case 'back-only':
          emailPrefix = cleanName + generate4Digits();
          break;
        case 'front-middle':
          emailPrefix = generate4Digits() + frontPart + generate4Digits() + backPart;
          break;
        case 'middle-back':
          emailPrefix = frontPart + generate4Digits() + backPart + generate4Digits();
          break;
        case 'front-back':
          emailPrefix = generate4Digits() + cleanName + generate4Digits();
          break;
        case 'all-three':
          emailPrefix = generate4Digits() + frontPart + generate4Digits() + backPart + generate4Digits();
          break;
        default:
          emailPrefix = cleanName + generate4Digits();
      }

      email = emailPrefix + '@techexpresser.com';

      // 检查数据库中是否已存在
      const existing = await db.select().from(generatedEmails).where(eq(generatedEmails.email, email)).limit(1);

      if (existing.length === 0) {
        break; // 找到唯一的邮箱
      }

      attempts++;
    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      return NextResponse.json(
        { success: false, error: '无法生成唯一邮箱，请重试' },
        { status: 500 }
      );
    }

    // 保存到数据库
    const [newEmail] = await db.insert(generatedEmails).values({
      email,
    }).returning();

    return NextResponse.json({
      success: true,
      email: newEmail,
    });
  } catch (error) {
    console.error('Error generating email:', error);
    return NextResponse.json(
      { success: false, error: '生成邮箱失败' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  return withAuth(request, handleGenerateEmail);
}
