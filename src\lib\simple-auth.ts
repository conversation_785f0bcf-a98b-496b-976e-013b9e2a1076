import { NextRequest, NextResponse } from "next/server";

export interface SimpleAuthenticatedRequest extends NextRequest {
  // 可以添加其他属性如果需要
}

/**
 * 简单的API认证中间件
 * 检查Authorization header中的密码
 */
export async function withSimpleAuth(
  request: NextRequest,
  handler: (req: SimpleAuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const authHeader = request.headers.get("authorization");
    const expectedPassword = process.env.API_AUTH_PASSWORD;

    if (!expectedPassword) {
      return NextResponse.json(
        { error: "Server configuration error", message: "API_AUTH_PASSWORD not configured" },
        { status: 500 }
      );
    }

    if (!authHeader) {
      return NextResponse.json(
        { 
          error: "Unauthorized", 
          message: "Authorization header required. Use 'Authorization: Bearer <password>' or 'Authorization: <password>'" 
        },
        { status: 401 }
      );
    }

    // 支持 "Bearer password" 或直接 "password" 格式
    const password = authHeader.startsWith("Bearer ") 
      ? authHeader.substring(7) 
      : authHeader;

    if (password !== expectedPassword) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Invalid credentials" },
        { status: 401 }
      );
    }

    return handler(request as SimpleAuthenticatedRequest);
  } catch (error) {
    console.error("Simple auth middleware error:", error);
    return NextResponse.json(
      { error: "Internal Server Error", message: "Authentication failed" },
      { status: 500 }
    );
  }
}
